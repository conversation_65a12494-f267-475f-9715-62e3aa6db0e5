import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

interface EmojiReaction {
  emoji: string | null;
  mood: string | null;
}

interface EmojiReactionState {
  [userId: string]: EmojiReaction;
}

const initialState: EmojiReactionState = {};

const emojiReactionSlice = createSlice({
  name: 'emojiReaction',
  initialState,
  reducers: {
    setUserEmojiReaction: (state, action: PayloadAction<{ 
      userId: string; 
      emoji: string | null; 
      mood: string | null; 
    }>) => {
      const { userId, emoji, mood } = action.payload;
      if (emoji && mood) {
        state[userId] = { emoji, mood };
      } else {
        delete state[userId];
      }
    },
    clearUserEmojiReaction: (state, action: PayloadAction<string>) => {
      const userId = action.payload;
      delete state[userId];
    },
    clearAllEmojiReactions: (state) => {
      return {};
    }
  }
});

export const { 
  setUserEmojiReaction, 
  clearUserEmojiReaction, 
  clearAllEmojiReactions 
} = emojiReactionSlice.actions;

// Selectors
export const selectEmojiReactions = (state: RootState) => state.emojiReaction;
export const selectUserEmojiReaction = (userId: string) => (state: RootState) => 
  state.emojiReaction[userId] || { emoji: null, mood: null };

export default emojiReactionSlice.reducer;
