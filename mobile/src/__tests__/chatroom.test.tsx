import React from 'react';
import { render } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';

import ChatRoom from '../components/ChatRoom';
import authReducer from '../redux/slices/authSlice';
import chatDBReducer from '../redux/slices/chatDBSlice';
import socketReducer from '../redux/slices/socketSlice';
import typingReducer from '../redux/slices/typingSlice';
import emojiReactionReducer from '../redux/slices/emojiReactionSlice';
import { RootStackParamList } from '../navigation/types';

// Mock the database service
jest.mock('../database/service', () => ({
  chatDBService: {
    observeMessages: jest.fn(() => ({
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    })),
    markMessagesAsRead: jest.fn(() => Promise.resolve()),
  }
}));

// Mock the WatermelonDB hook
jest.mock('../hooks/useWatermelonObservable', () => ({
  useWatermelonObservable: jest.fn(() => []),
}));

// Mock react-native-modal
jest.mock('react-native-modal', () => {
  const { View } = require('react-native');
  return ({ children, isVisible }: any) => isVisible ? <View>{children}</View> : null;
});

const Stack = createStackNavigator<RootStackParamList>();

const createTestStore = () => {
  return configureStore({
    reducer: {
      auth: authReducer,
      chatDB: chatDBReducer,
      socket: socketReducer,
      typing: typingReducer,
      emojiReaction: emojiReactionReducer,
    },
    preloadedState: {
      auth: {
        user: 'testuser',
        token: 'test-token',
        isAuthenticated: true,
        loading: false,
        error: null,
      },
      chatDB: {
        initialized: true,
        currentRoomId: null,
        currentReceiverUsername: 'testuser2',
      },
      socket: {
        socket: null,
        connected: true,
        connecting: false,
        error: null,
        serverUrl: 'ws://localhost:3000',
        authToken: 'test-token',
        messages: [],
      },
      typing: {},
      emojiReaction: {},
    },
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({ thunk: false }),
  });
};

const renderWithProviders = (component: React.ReactElement) => {
  const store = createTestStore();
  
  return render(
    <Provider store={store}>
      <NavigationContainer>
        <Stack.Navigator>
          <Stack.Screen name="ChatRoom" component={() => component} />
        </Stack.Navigator>
      </NavigationContainer>
    </Provider>
  );
};

describe('ChatRoom Component', () => {
  const mockRoute = {
    key: 'ChatRoom',
    name: 'ChatRoom' as const,
    params: { username: 'testuser2' },
  };

  const mockNavigation = {
    navigate: jest.fn(),
    goBack: jest.fn(),
    dispatch: jest.fn(),
    setParams: jest.fn(),
    addListener: jest.fn(),
    removeListener: jest.fn(),
    canGoBack: jest.fn(),
    isFocused: jest.fn(),
    push: jest.fn(),
    replace: jest.fn(),
    pop: jest.fn(),
    popToTop: jest.fn(),
    setOptions: jest.fn(),
    reset: jest.fn(),
    getParent: jest.fn(),
    getState: jest.fn(),
    getId: jest.fn(),
  };

  test('renders ChatRoom component', () => {
    const { getByText } = renderWithProviders(
      <ChatRoom route={mockRoute} navigation={mockNavigation} />
    );

    // Should render the chat window
    expect(getByText('testuser2')).toBeTruthy();
  });

  test('displays no messages text when there are no messages', () => {
    const { getByText } = renderWithProviders(
      <ChatRoom route={mockRoute} navigation={mockNavigation} />
    );

    expect(getByText('No messages yet. Start the conversation!')).toBeTruthy();
  });
});
