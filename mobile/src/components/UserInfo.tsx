import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useAppSelector } from '../hooks/redux';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';

interface UserInfoProps {
  userId: string; // This is now actually a username
  className?: string;
  lastMessage?: string; // Optional prop for last message
  status?: string; // Optional prop for status
  showEmojiReactions?: boolean; // Whether to show emoji reactions in room list
  disableEmoji?: boolean; // Whether to disable emoji visibility completely
  onClick?: () => void; // Optional click handler for the user info component
  style?: any; // Additional styles
}

const UserInfo: React.FC<UserInfoProps> = ({
  userId,
  lastMessage,
  status,
  showEmojiReactions = true, // Default to true
  disableEmoji = false, // Default to false (emojis are visible)
  onClick,
  style
}) => {
  // Check if this user has an active emoji reaction (only if emojis are not disabled)
  const emojiReaction = useAppSelector((state) =>
    showEmojiReactions && !disableEmoji ? selectUserEmojiReaction(userId)(state) : null
  );

  // Display first letter of username as avatar
  const getAvatarText = () => {
    return userId.charAt(0).toUpperCase();
  };

  // Display username
  const getDisplayName = () => {
    return userId;
  };

  const renderTypingIndicator = () => {
    return (
      <View style={styles.typingContainer}>
        <Text style={styles.typingText}>Typing</Text>
        <View style={styles.typingIndicator}>
          <View style={[styles.typingDot, styles.typingDot1]} />
          <View style={[styles.typingDot, styles.typingDot2]} />
          <View style={[styles.typingDot, styles.typingDot3]} />
        </View>
      </View>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity style={styles.avatar} onPress={onClick}>
        <Text style={styles.avatarText}>{getAvatarText()}</Text>
      </TouchableOpacity>
      
      <View style={styles.details}>
        <TouchableOpacity onPress={onClick}>
          <Text style={styles.userName}>{getDisplayName()}</Text>
        </TouchableOpacity>

        {/* Show emoji reaction if active, otherwise show last message */}
        {emojiReaction ? (
          <View style={styles.emojiReactionContainer}>
            <Text style={styles.emojiReaction}>{emojiReaction.emoji}</Text>
          </View>
        ) : lastMessage ? (
          <Text style={styles.lastMessage} numberOfLines={1}>
            {lastMessage}
          </Text>
        ) : null}

        {/* Conditionally render status if it exists */}
        {status && (
          <View style={styles.statusContainer}>
            {status === 'Typing' ? (
              renderTypingIndicator()
            ) : (
              <Text style={styles.status}>{status}</Text>
            )}
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
  },
  details: {
    flex: 1,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  lastMessage: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  emojiReactionContainer: {
    marginBottom: 2,
  },
  emojiReaction: {
    fontSize: 16,
  },
  statusContainer: {
    marginTop: 2,
  },
  status: {
    fontSize: 12,
    color: '#999',
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    fontSize: 12,
    color: '#2196F3',
    marginRight: 6,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: '#2196F3',
    marginHorizontal: 1,
  },
  typingDot1: {
    // Animation would be added here in a real implementation
  },
  typingDot2: {
    // Animation would be added here in a real implementation
  },
  typingDot3: {
    // Animation would be added here in a real implementation
  },
});

export default UserInfo;
